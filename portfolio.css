body {
  background-color: #2e382e;
  min-height: 5000px;
  /* border:2px solid black; */
  margin: 0;
  font-family: "DM Sans";
}
.facebook {
  padding: 20px;
  font-size: 30px;
  width: 40px;
  text-align: center;
  text-decoration: solid;
  margin: 5px;

  opacity: 0.7;
}

.para {
  font-size: 70px;
}
.me {
  width: 310px;
  height: 370px;
  border-radius: 220px;
  clip-path: ellipse(50% 50% at 50% 50%);
  margin-left: 40px;
  margin-top: 50px;
  
  position: relative;
  animation: blobAnimation 5s infinite;
}

@keyframes blobAnimation {
  0%, 100% {
      border-radius: 50% 50% 50% 50%;
  }
  25% {
      border-radius: 60% 40% 60% 40%;
  }
  50% {
      border-radius: 40% 60% 40% 60%;
  }
  75% {
      border-radius: 50% 60% 40% 50%;
  }
}

.first-div {
  margin-top: 25px;
  /* margin-left: 68%; */
  width: 100%;
  /* border: 2px solid black; */
  display: flex;
  justify-content: space-between;
}

.second-div {
  /* border: 2px solid black; */
  display: flex;
  justify-content: center;
  justify-items: center;
}

.navbar-wrapper {
  /* border: 2px solid black; */
  margin-top: 20px;
}

.smart {
  margin-left: 20px;
  /* border: 2px solid black; */
  margin-bottom: 50px;
  color:#50c9ce;
  width:135px;
  border:1px solid #50c9ce;
  border-radius:3px;
  padding-left: 7px;
}

.navbar {
  text-decoration: solid;
  margin-right: 60px;
  color: #2e382e;
  font-weight: bold;
  opacity: 0.8;
}

.navbar:last-child {
  margin-right: 0;
}

.word {
  /* position:absolute; */
  margin-top: 40px;
  font-size: 40px;
  color: #50c9ce;
  display: flex;
  justify-content: center;
  /* border: 2px solid black; */
  margin-left: 40px;
}

.img-container {
  display: flex;
  /* border: 2px solid black; */
  justify-content: center;

}

.form {
  margin-top:50px;
  border-radius: 15px;
  width: 80%;
  height: 400px;
  background-color: transparent;
  border:2px solid #50c9ce;
}

.form-parent {
  width: 100%;
  /* margin-top: 230px; */
  display: flex;
  justify-content: space-around;
  /* border: 2px solid black; */
}

.parent-about {
  /* border: 2px solid black; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  justify-items: center;
}

button {
  height: 40px;
  width: 100px;
  background-color: aqua;
  border: none;
  border-radius: 10px;
  /* border-color:#e2f3f4; */
  /* border: 2px solid black; */
  margin-right: 10px;
}

/* .home__social {
  display: grid;
  grid-template-columns: max-content;
  row-gap: 1rem;
} */

.home__social-icon {
  font-size: 2rem;
  color: black;
  background-color: #50c9ce;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  text-align: center;
  
}
.home__social-icon:hover {
  color: white;
}

.social-icons {
  /* border: 2px solid black; */
  margin-top: 60px;
  /* border: 2px solid black; */
  display: flex;
  justify-content: center;
  gap: 30px;
}


.social-wrapper {
  border:2px solid #50c9ce;
  display: flex;
  gap: 40px;
  padding-top: 10px;
  padding-left:30px;
  border-radius:23px;
  width:230px;
  height:50px;
}

.skills-title {
  /* border: 2px solid black; */
  margin-top: 200px;
  font-size: 40px;
  color: #50c9ce;
  display: flex;
  justify-content: center;
  
}
 
.but{
  margin-top: 50px;
  border-radius: 15px;
  width: 50%;
  height: 400px;
  background-color: transparent;
  border:2px solid #50c9ce;
}

.but-parent{
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.button-more{
  width:220px;
  height:70px;
  background-color: transparent;
  border:3px solid #50c9ce;
  margin-left: 220px;
  margin-top:50px;
  padding: 10px 10px;
  cursor: pointer;
  animation: moveSideToSide 2s infinite;
}
@keyframes moveSideToSide {
  0% {
      transform: translateX(0);
  }
  50% {
      transform: translateX(100px);
  }
  100% {
      transform: translateX(0);
  }
}

.navi{
  text-decoration:none;
  font-size:30px;
  color:#50c9ce;
}

.navi:hover{
  color:white;
}

h3{
color:#cffafe;
text-align:center;
}

.emoji{
  font-size:50px;
  text-align:center;
  margin-top:20px;
}

.second{
  margin-top: 50px;
  border-radius: 15px;
  width: 50%;
  height: 400px;
  background-color: transparent;
  border:2px solid #50c9ce;
}
 
.second-parent{
  width: 100%;
  display: flex;
  justify-content: space-around;
}
.button-more2{
  width:220px;
  height:70px;
  background-color: transparent;
  border:3px solid #50c9ce;
  margin-left: 220px;
  margin-top:50px;
  padding: 10px 10px;
  cursor: pointer;
  animation: moveSideToSide 2s infinite;
}
@keyframes moveSideToSide {
  0% {
      transform: translateX(0);
  }
  50% {
      transform: translateX(100px);
  }
  100% {
      transform: translateX(0);
  }
}
